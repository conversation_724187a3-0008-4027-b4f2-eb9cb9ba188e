<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import PhoneNumberInput from './PhoneNumberInput.vue';
import type { ServiceConfiguration, PhoneNumberInput as PhoneNumberInputType } from '../types';

// Props
const props = defineProps<{
    modelValue: ServiceConfiguration;
    leadSources?: any;
    disabled?: boolean;
}>();

// Emits
const emit = defineEmits<{
    'update:modelValue': [value: ServiceConfiguration];
    'next': [];
}>();

// Local state
const config = ref<ServiceConfiguration>({ ...props.modelValue });
const phoneValidation = ref<Record<string, PhoneNumberInputType>>({});

// Computed
const isValid = computed(() => {
    if (config.value.useSharedNumber) {
        return config.value.sharedNumber && 
               phoneValidation.value.shared?.isValid &&
               (config.value.sms.enabled || config.value.call.enabled || config.value.ai_call.enabled);
    } else {
        const smsValid = !config.value.sms.enabled || 
                        (config.value.sms.number && phoneValidation.value.sms?.isValid);
        const callValid = !config.value.call.enabled || 
                         (config.value.call.number && phoneValidation.value.call?.isValid);
        const aiCallValid = !config.value.ai_call.enabled || 
                           (config.value.ai_call.number && phoneValidation.value.ai_call?.isValid);
        
        return smsValid && callValid && aiCallValid &&
               (config.value.sms.enabled || config.value.call.enabled || config.value.ai_call.enabled);
    }
});

const enabledServices = computed(() => {
    const services = [];
    if (config.value.sms.enabled) services.push('SMS');
    if (config.value.call.enabled) services.push('Voice Calls');
    if (config.value.ai_call.enabled) services.push('AI Calls');
    return services;
});

// Check if services are enabled in lead sources
const isServiceAvailable = (service: string) => {
    if (!props.leadSources) return true;
    return props.leadSources[service]?.status === 'enabled';
};

// Methods
const updateConfig = () => {
    emit('update:modelValue', config.value);
};

const onPhoneValidate = (service: string, phoneObject: PhoneNumberInputType) => {
    phoneValidation.value[service] = phoneObject;
};

const onSharedNumberToggle = () => {
    if (config.value.useSharedNumber) {
        // Clear individual numbers when using shared
        config.value.sms.number = '';
        config.value.call.number = '';
        config.value.ai_call.number = '';
    } else {
        // Clear shared number when using individual
        config.value.sharedNumber = '';
    }
    updateConfig();
};

const onServiceToggle = (service: 'sms' | 'call' | 'ai_call') => {
    config.value[service].enabled = !config.value[service].enabled;
    
    // If no services are enabled, enable at least one
    if (!config.value.sms.enabled && !config.value.call.enabled && !config.value.ai_call.enabled) {
        config.value.sms.enabled = true;
    }
    
    updateConfig();
};

const onNext = () => {
    if (isValid.value) {
        emit('next');
    }
};

// Watch for changes
watch(() => config.value, updateConfig, { deep: true });
</script>

<template>
    <div class="service-configuration">
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-surface-900 dark:text-surface-0 mb-2">
                Configure Communication Services
            </h3>
            <p class="text-surface-600 dark:text-surface-400">
                Select the communication services you want to enable and configure their phone numbers.
            </p>
        </div>

        <!-- Service Selection -->
        <Card class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-cog text-primary"></i>
                    Service Selection
                </div>
            </template>
            <template #content>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- SMS Service -->
                    <div class="service-card">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-comment text-blue-500"></i>
                                <span class="font-medium">SMS</span>
                            </div>
                            <ToggleButton
                                v-model="config.sms.enabled"
                                :disabled="!isServiceAvailable('sms') || disabled"
                                @change="onServiceToggle('sms')"
                                onLabel="Enabled"
                                offLabel="Disabled"
                                class="w-20"
                            />
                        </div>
                        <p class="text-sm text-surface-600 dark:text-surface-400">
                            Send and receive SMS messages
                        </p>
                        <div v-if="!isServiceAvailable('sms')" class="mt-2">
                            <small class="text-orange-500">
                                <i class="pi pi-exclamation-triangle mr-1"></i>
                                SMS not enabled in lead sources
                            </small>
                        </div>
                    </div>

                    <!-- Voice Calls Service -->
                    <div class="service-card">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-phone text-green-500"></i>
                                <span class="font-medium">Voice Calls</span>
                            </div>
                            <ToggleButton
                                v-model="config.call.enabled"
                                :disabled="!isServiceAvailable('call') || disabled"
                                @change="onServiceToggle('call')"
                                onLabel="Enabled"
                                offLabel="Disabled"
                                class="w-20"
                            />
                        </div>
                        <p class="text-sm text-surface-600 dark:text-surface-400">
                            Make and receive voice calls
                        </p>
                        <div v-if="!isServiceAvailable('call')" class="mt-2">
                            <small class="text-orange-500">
                                <i class="pi pi-exclamation-triangle mr-1"></i>
                                Voice calls not enabled in lead sources
                            </small>
                        </div>
                    </div>

                    <!-- AI Calls Service -->
                    <div class="service-card">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center gap-2">
                                <i class="pi pi-android text-purple-500"></i>
                                <span class="font-medium">AI Calls</span>
                            </div>
                            <ToggleButton
                                v-model="config.ai_call.enabled"
                                :disabled="!isServiceAvailable('ai_call') || disabled"
                                @change="onServiceToggle('ai_call')"
                                onLabel="Enabled"
                                offLabel="Disabled"
                                class="w-20"
                            />
                        </div>
                        <p class="text-sm text-surface-600 dark:text-surface-400">
                            AI-powered automated calls
                        </p>
                        <div v-if="!isServiceAvailable('ai_call')" class="mt-2">
                            <small class="text-orange-500">
                                <i class="pi pi-exclamation-triangle mr-1"></i>
                                AI calls not enabled in lead sources
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Enabled Services Summary -->
                <div v-if="enabledServices.length > 0" class="mt-4 p-3 bg-primary-50 dark:bg-primary-900/20 rounded-lg">
                    <div class="flex items-center gap-2 text-primary-700 dark:text-primary-300">
                        <i class="pi pi-check-circle"></i>
                        <span class="font-medium">Enabled Services:</span>
                        <span>{{ enabledServices.join(', ') }}</span>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Number Configuration -->
        <Card class="mb-6">
            <template #title>
                <div class="flex items-center gap-2">
                    <i class="pi pi-phone text-primary"></i>
                    Phone Number Configuration
                </div>
            </template>
            <template #content>
                <!-- Shared Number Option -->
                <div class="mb-6">
                    <div class="flex items-center gap-3 mb-4">
                        <Checkbox
                            v-model="config.useSharedNumber"
                            :disabled="disabled"
                            @change="onSharedNumberToggle"
                            inputId="useSharedNumber"
                        />
                        <label for="useSharedNumber" class="font-medium">
                            Use one number for all services
                        </label>
                    </div>
                    <p class="text-sm text-surface-600 dark:text-surface-400 ml-6">
                        Use a single phone number for SMS, voice calls, and AI calls (recommended for cost efficiency)
                    </p>
                </div>

                <!-- Shared Number Input -->
                <div v-if="config.useSharedNumber" class="mb-6">
                    <PhoneNumberInput
                        v-model="config.sharedNumber"
                        label="Shared Phone Number"
                        placeholder="Enter phone number for all services"
                        :required="true"
                        :disabled="disabled"
                        @validate="onPhoneValidate('shared', $event)"
                    />
                </div>

                <!-- Individual Number Inputs -->
                <div v-else class="space-y-6">
                    <!-- SMS Number -->
                    <div v-if="config.sms.enabled">
                        <PhoneNumberInput
                            v-model="config.sms.number"
                            label="SMS Phone Number"
                            placeholder="Enter phone number for SMS"
                            :required="true"
                            :disabled="disabled"
                            @validate="onPhoneValidate('sms', $event)"
                        />
                    </div>

                    <!-- Voice Call Number -->
                    <div v-if="config.call.enabled">
                        <PhoneNumberInput
                            v-model="config.call.number"
                            label="Voice Call Phone Number"
                            placeholder="Enter phone number for voice calls"
                            :required="true"
                            :disabled="disabled"
                            @validate="onPhoneValidate('call', $event)"
                        />
                    </div>

                    <!-- AI Call Number -->
                    <div v-if="config.ai_call.enabled">
                        <PhoneNumberInput
                            v-model="config.ai_call.number"
                            label="AI Call Phone Number"
                            placeholder="Enter phone number for AI calls"
                            :required="true"
                            :disabled="disabled"
                            @validate="onPhoneValidate('ai_call', $event)"
                        />
                    </div>
                </div>
            </template>
        </Card>

        <!-- Next Button -->
        <div class="flex justify-end">
            <Button
                label="Continue to Purchase"
                icon="pi pi-arrow-right"
                iconPos="right"
                :disabled="!isValid || disabled"
                @click="onNext"
                class="px-6 py-3"
            />
        </div>
    </div>
</template>

<style scoped>
.service-card {
    @apply p-4 border border-surface-200 dark:border-surface-700 rounded-lg;
}

.service-card:hover {
    @apply border-primary-200 dark:border-primary-700;
}
</style>
