import {
    assemblyAIApiKey,
    facebookAppId,
    facebookPageAccessToken,
    facebookVerifyToken,
    googleServiceAccountEmail,
    googleServiceAccountKey,
    invokeTwilio,
    openAIKey,
    requireAuth,
    sendGridApiKey,
    telegramBotToken,
    twilioAccounSID,
    twilioAuthTpken,
    twilioWhatsappNumber
} from './utils';
import sgMail from '@sendgrid/mail';
import { onCall, HttpsError } from 'firebase-functions/v2/https';

export const onValidateFeatures = onCall(
    {
        cors: true,
        secrets: [openAIKey, twilioAuthTpken, twilioAccounSID, twilioWhatsappNumber, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey, googleServiceAccountEmail, googleServiceAccountKey, facebookAppId],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        const { feature, toValidate } = data;
        try {
            if (!feature) {
                throw new HttpsError('invalid-argument', 'Feature is required');
            }
            if (!toValidate) {
                throw new HttpsError('invalid-argument', 'Value to validate is required');
            }

            if (!['email'].includes(feature)) {
                throw new HttpsError('invalid-argument', 'Feature not supported');
            }

            if (feature === 'email') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(toValidate)) {
                    throw new HttpsError('invalid-argument', 'Invalid email format');
                }
                const msg = {
                    to: toValidate, // Recipient's email address
                    from: '<EMAIL>', // Dynamically set the sender email
                    subject: 'Liftt Email Validation Test', // Email subject
                    html: 'This is a test email from Liftt. please ignore.'
                };
                sgMail.setApiKey(sendGridApiKey.value());
                await sgMail.send(msg);
            } else if (feature === 'sms') {
                const twilio = invokeTwilio();
                const from = twilioWhatsappNumber.value();
                await twilio.calls.create({
                    from: from,
                    body: 'This is a test message from Liftt. please ignore.',
                    to: toValidate
                });
            } else if (feature === 'call') {
            }
        } catch (err) {}
    }
);
